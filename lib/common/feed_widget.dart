import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:praja/common/widgets/focus_detector.dart';
import 'package:praja/constants/praja_constants.dart';
import 'package:praja/features/creative_carousel/creative_carousel_widget.dart';
import 'package:praja/features/creative_carousel/models/creative_carousel.dart';
import 'package:praja/features/fan_requests/widgets/fan_requests_feed_item_widget.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/my_feed/widgets/feed_error_widget.dart';
import 'package:praja/features/post/create/widgets/post_creation_status_widget.dart';
import 'package:praja/features/posters/models/poster_carousel.dart';
import 'package:praja/features/posters/models/subscription_banner.dart';
import 'package:praja/features/posters/poster_carousel_feed_widget/poster_carousel_mode.dart';
import 'package:praja/features/posters/widgets/poster_carousel_widget.dart';
import 'package:praja/features/posters/widgets/subscription_banner_widget.dart';
import 'package:praja/features/premium_experience/models/events_media_carousel.dart';
import 'package:praja/features/premium_experience/models/faqs.dart';
import 'package:praja/features/premium_experience/models/my_poster_styles.dart';
import 'package:praja/features/premium_experience/models/offer_feed_item.dart';
import 'package:praja/features/premium_experience/models/payment_feed_item.dart';
import 'package:praja/features/premium_experience/models/premium_members.dart';
import 'package:praja/features/premium_experience/models/profile_views.dart';
import 'package:praja/features/premium_experience/models/relation_manager.dart';
import 'package:praja/features/premium_experience/models/subscription_info_toast.dart';
import 'package:praja/features/premium_experience/models/upcoming_events.dart';
import 'package:praja/features/premium_experience/models/usage_counts.dart';
import 'package:praja/features/premium_experience/offer_reveal_sheet/offer_feed_item_widget.dart';
import 'package:praja/features/premium_experience/widgets/events_media_carousel_widget.dart';
import 'package:praja/features/premium_experience/widgets/faqs_widget.dart';
import 'package:praja/features/premium_experience/widgets/my_poster_styles_widget.dart';
import 'package:praja/features/premium_experience/widgets/payment_feed_item_widget.dart';
import 'package:praja/features/premium_experience/widgets/premium_members_widget.dart';
import 'package:praja/features/premium_experience/widgets/profile_views_widget.dart';
import 'package:praja/features/premium_experience/widgets/relation_manager_widget.dart';
import 'package:praja/features/premium_experience/widgets/subscription_info_toast_widget.dart';
import 'package:praja/features/premium_experience/widgets/upcoming_events_widget.dart';
import 'package:praja/features/premium_experience/widgets/usage_counts_widget.dart';
import 'package:praja/features/selection_form/selection_form_widget.dart';
import 'package:praja/features/suggestions/models/suggested_section.dart';
import 'package:praja/features/video_posters/models/video_poster_carousel.dart';
import 'package:praja/features/video_posters/video_poster_carousel_widget.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/badge_card.dart';
import 'package:praja/models/error_feed_item.dart';
import 'package:praja/models/fan_requests_section.dart';
import 'package:praja/models/feed_toast.dart';
import 'package:praja/models/post.dart';
import 'package:praja/models/post_creation_status_feed_item.dart';
import 'package:praja/models/post_shimmer_feed_item.dart';
import 'package:praja/models/referral.dart';
import 'package:praja/models/selection_form.dart';
import 'package:praja/models/v2/trending_hashtags.dart';
import 'package:praja/screens/hashtags/trending_hashtags_list_item.dart';
import 'package:praja/screens/posts/list_item.dart';
import 'package:praja/screens/posts/suggested_list_widget.dart';
import 'package:praja/services/event_flag.dart';
import 'package:praja/shimmers/post.dart';
import 'package:praja/styles.dart';
import 'package:praja/utils/ui_utils.dart';
import 'package:praja/utils/widgets.dart';
import 'package:praja/utils/widgets/badge_card_widget.dart';
import 'package:praja/common/feed_toast/feed_toast_item.dart';
import 'package:praja/utils/widgets/referral_card.dart';

class FeedWidget extends StatefulWidget {
  final EdgeInsets? padding;
  final ScrollController? scrollController;
  final PagingController<dynamic, dynamic> pagingController;
  final String source;
  final Function()? viewAllSuggested;
  final Function() newPageErrorIndicatorTryAgain;
  final Function() firstPageErrorIndicatorTryAgain;
  final Widget? firstPageProgressIndicatorBuilderWidget;
  final int? sourceId;
  final ScrollPhysics? scrollPhysics;
  final bool shrinkWrap;
  final String? emptyPostWidgetText;
  final IndexedWidgetBuilder? separatorBuilder;

  /// This is only for My Feed as we are using custom builder
  final Function(Object? error)? onMyFeedErrorTryAgain;
  final bool enableTrendTutorial;
  final int? highlightPostId;
  final bool highlightPostCompleted;
  final VoidCallback? onHighlightPostCompleted;
  final VoidCallback? onRefresh;

  const FeedWidget(
      {super.key,
      this.padding,
      this.scrollController,
      required this.pagingController,
      required this.source,
      required this.newPageErrorIndicatorTryAgain,
      required this.firstPageErrorIndicatorTryAgain,
      this.firstPageProgressIndicatorBuilderWidget,
      this.viewAllSuggested,
      this.scrollPhysics,
      this.sourceId,
      this.shrinkWrap = false,
      this.emptyPostWidgetText,
      this.separatorBuilder,
      this.onMyFeedErrorTryAgain,
      this.enableTrendTutorial = true,
      this.highlightPostId,
      this.highlightPostCompleted = false,
      this.onHighlightPostCompleted,
      this.onRefresh});

  @override
  State<FeedWidget> createState() => _FeedWidgetState();
}

class _FeedWidgetState extends State<FeedWidget> {
  String getEmptyPostWidgetText() {
    return widget.emptyPostWidgetText ??
        context.getString(StringKey.noPostsFoundText);
  }

  @override
  Widget build(BuildContext context) {
    return PagedListView.separated(
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      separatorBuilder: widget.separatorBuilder ??
          (context, index) {
            return Container(
              height: 4,
              color: Styles.backgroundBlue,
            );
          },
      physics: widget.scrollPhysics,
      cacheExtent: PRAJAConstants.cacheExtent,
      scrollController: widget.scrollController,
      pagingController: widget.pagingController,
      builderDelegate: PagedChildBuilderDelegate(
        noItemsFoundIndicatorBuilder: (context) {
          return Widgets.emptyPostWidget(msg: getEmptyPostWidgetText());
        },
        newPageProgressIndicatorBuilder: (context) {
          return const PostShimmer();
        },
        firstPageProgressIndicatorBuilder: (context) {
          return widget.firstPageProgressIndicatorBuilderWidget ??
              const PostShimmer();
        },
        firstPageErrorIndicatorBuilder: (context) {
          return PageErrorIndicator(
            source: widget.source,
            sourceId: widget.sourceId,
            error: widget.pagingController.error.toString(),
            onTryAgain: widget.firstPageErrorIndicatorTryAgain,
          );
        },
        newPageErrorIndicatorBuilder: (context) {
          return PageErrorIndicator(
            source: widget.source,
            sourceId: widget.sourceId,
            error: widget.pagingController.error.toString(),
            pageNumber: widget.pagingController.value.nextPageKey,
            onTryAgain: widget.newPageErrorIndicatorTryAgain,
          );
        },
        itemBuilder: (context, item, index) {
          return FeedItemBuilder(
            item: item,
            source: widget.source,
            sourceId: widget.sourceId,
            index: index,
            isFirstElementInList: index == 0,
            enableTrendTutorial: widget.enableTrendTutorial,
            viewAllSuggested: widget.viewAllSuggested,
            onFeedErrorTryAgain: widget.onMyFeedErrorTryAgain,
            highlightPostId: widget.highlightPostId,
            highlightPostCompleted: widget.highlightPostCompleted,
            onHighlightPostCompleted: widget.onHighlightPostCompleted,
            onRefresh: widget.onRefresh,
          );
        },
      ),
    );
  }
}

class FeedItemBuilder extends StatelessWidget {
  final dynamic item;
  final String source;
  final int? sourceId;
  final int index;
  final bool isFirstElementInList;
  final bool enableTrendTutorial;
  final Function()? viewAllSuggested;
  final Function(Object? error)? onFeedErrorTryAgain;
  final int? highlightPostId;
  final bool highlightPostCompleted;
  final VoidCallback? onHighlightPostCompleted;
  final VoidCallback? onRefresh;

  const FeedItemBuilder(
      {super.key,
      required this.item,
      required this.source,
      required this.index,
      required this.isFirstElementInList,
      this.sourceId,
      this.enableTrendTutorial = true,
      this.viewAllSuggested,
      this.onFeedErrorTryAgain,
      this.highlightPostId,
      this.highlightPostCompleted = false,
      this.onHighlightPostCompleted,
      this.onRefresh});

  static bool _isHashtagShown = false;

  @override
  Widget build(BuildContext context) {
    if (item is Post) {
      final tag = "post-${item.id}-$source";
      return Hero(
        tag: tag,
        key: Key(tag),
        child: PostListItem(
          source: source,
          index: index,
          onHighlightCompleted: onHighlightPostCompleted,
          highlightPostId: highlightPostId,
          highlightPostCompleted: highlightPostCompleted,
          heroTag: tag,
          post: item,
          isFirstElementInList: index == 0,
          enableTrendTutorial: enableTrendTutorial,
          user: item.user,
          circle: item.circle,
        ),
      );
    } else if (item is TrendingHashtags) {
      return FocusDetector(
          onVisibilityGained: () {
            if (!_isHashtagShown &&
                !GetIt.I.get<EventFlags>().isFeedLoadingInBackground) {
              AppAnalytics.logEvent(name: "trending_topics");
              _isHashtagShown = true;
            } else {
              GetIt.I.get<EventFlags>().isTrendingTopicsLoaded = true;
            }
          },
          child: TrendingHashTagListItem(trendingHashtags: item));
    } else if (item is Referral) {
      return Padding(
        padding: index == 0
            ? const EdgeInsets.only(top: 10)
            : const EdgeInsets.all(0),
        child: ReferralCard(
          referral: item,
        ),
      );
    } else if (item is SuggestedSection) {
      return SuggestedListWidget(
        source: source,
        suggestedList: item,
        viewAllSuggested: viewAllSuggested,
      );
    } else if (item is FeedToast) {
      return Padding(
        padding: index == 0
            ? const EdgeInsets.only(bottom: 5)
            : const EdgeInsets.all(0),
        child: FeedToastItem(
          feedToast: item,
          source: source,
        ),
      );
    } else if (item is BadgeCard) {
      return BadgeCardWidget(
        badgeCard: item,
        source: "my_feed",
      );
    } else if (item is CreativeCarousel) {
      return CreativeCarouselWidget(
        creativeCarousel: item,
        source: source,
        scrollIndex: index,
      );
    } else if (item is SubscriptionBanner) {
      return SubscriptionBannerWidget(
        subscriptionBanner: item,
        source: source,
      );
    } else if (item is SelectionForm) {
      return SelectionFormWidget(selectionForm: item, onRefresh: onRefresh);
    } else if (item is FanRequestsSection) {
      return FanRequestsFeedItemWidget(
        fanRequestsSection: item,
        source: source,
      );
    } else if (item is PosterCarousel) {
      return PosterCarouselWidget(
        key: Key("poster-carousel-$index-$source"),
        posterCarousel: item,
        source: source,
        mode: PosterCarouselMode.light,
      );
    } else if (item is VideoPosterCarousel) {
      return VideoPosterCarouselWidget(
        key: Key("video-poster-carousel-$index-$source"),
        carousel: item,
        source: source,
        pageIndex: index,
      );
    } else if (item is SubscriptionInfoToast) {
      return SubscriptionInfoToastWidget(subscriptionInfoToast: item);
    } else if (item is RelationManager) {
      return RelationManagerWidget(
          relationManager: item, source: source, index: index);
    } else if (item is UsageCounts) {
      return UsageCountsWidget(usageCounts: item, source: source, index: index);
    } else if (item is PremiumMembers) {
      return PremiumMembersWidget(
          premiumMembers: item, source: source, index: index);
    } else if (item is UpcomingEvents) {
      return UpcomingEventsWidget(
          upcomingEvents: item, source: source, index: index);
    } else if (item is MyPosterStyles) {
      return MyPosterStylesWidget(
          myPosterStyles: item, source: source, index: index);
    } else if (item is ProfileViews) {
      return ProfileViewsWidget(
          profileViews: item, source: source, index: index);
    } else if (item is EventsMediaCarousel) {
      return EventsMediaCarouselWidget(
          eventsMediaCarousel: item, source: source, index: index);
    } else if (item is PostCreationStatusFeedItem) {
      return const PostCreationStatusWidget();
    } else if (item is PaymentFeedItem) {
      return PaymentFeedItemWidget(
          paymentFeedItem: item, source: source, index: index);
    } else if (item is OfferFeedItem) {
      return OfferFeedItemWidget(offerItem: item, source: source, index: index);
    } else if (item is FAQs) {
      return FAQsWidget(faqs: item, source: source, index: index);
    } else if (item is PostShimmerFeedItem) {
      return const PostShimmer();
    } else if (item is ErrorFeedItem) {
      return FeedErrorWidget(
        onTryAgain: (error) => onFeedErrorTryAgain?.call(item.error),
      );
    } else {
      return Container();
    }
  }
}
