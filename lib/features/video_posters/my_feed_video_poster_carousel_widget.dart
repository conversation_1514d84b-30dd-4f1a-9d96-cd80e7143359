import 'package:flutter/material.dart';
import 'package:praja/features/video_posters/models/video_poster_carousel.dart';
import 'package:praja/features/video_posters/video_poster_carousel_widget.dart';

class MyFeedVideoPosterCarouselWidget extends StatelessWidget {
  final VideoPosterCarousel carousel;
  final String source;
  final int pageIndex;

  const MyFeedVideoPosterCarouselWidget({
    super.key,
    required this.carousel,
    required this.source,
    required this.pageIndex,
  });

  @override
  Widget build(BuildContext context) {
    // Get device width and calculate responsive dimensions
    final screenWidth = MediaQuery.of(context).size.width;
    // final containerWidth = screenWidth * 0.7; // 70% of screen width for margins

    // Calculate height based on aspect ratio
    final aspectRatio = carousel.frameWidth / carousel.frameHeight;
    final videoHeight = screenWidth / aspectRatio;

    // Use screen height as reference to ensure we have enough space
    final screenHeight = MediaQuery.of(context).size.height;
    final maxAllowedHeight = screenHeight * 0.8; // 80% of screen height

    // Use the larger of calculated height + padding or a minimum height
    final calculatedHeight = videoHeight; // Reasonable padding for UI elements
    final totalHeight = calculatedHeight > maxAllowedHeight
        ? maxAllowedHeight
        : calculatedHeight;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        children: [
          SizedBox(
            width: screenWidth,
            height: totalHeight,
            child: VideoPosterCarouselWidget(
              carousel: carousel,
              source: source,
              pageIndex: pageIndex,
            ),
          ),
        ],
      ),
    );
  }
}
