import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:praja/features/video_posters/models/video_poster_carousel.dart';
import 'package:praja/features/video_posters/video_poster_carousel_widget.dart';

/// A feed-optimized wrapper for [VideoPosterCarouselWidget].
///
/// This widget was created to adapt the full-screen [VideoPosterCarouselWidget]
/// for use in feed contexts where bounded constraints are required.
///
/// **Why this wrapper is needed:**
/// - [VideoPosterCarouselWidget] was designed for full-screen layouts with unbounded height
/// - It uses Spacer widgets that require unlimited vertical space to expand
/// - Direct use in feeds causes overflow issues due to constraint mismatches
/// - Feed contexts need widgets with defined heights for proper scrolling
///
/// **How it solves the constraint problem:**
/// - Uses [AspectRatio] to provide proper height constraints based on video dimensions
/// - Uses [FittedBox] to automatically scale content if it exceeds available space
/// - Uses [SizedBox] to give [VideoPosterCarouselWidget] the exact bounded constraints it needs
/// - Maintains original aspect ratios while preventing overflow issues
///
/// **Constraint flow:**
/// Feed (unbounded height) → AspectRatio (calculated height) → FittedBox (scaled) →
/// SizedBox (exact bounds) → VideoPosterCarouselWidget (thinks it's full-screen)
class MyFeedVideoPosterCarouselWidget extends StatelessWidget {
  final VideoPosterCarousel carousel;
  final String source;
  final int pageIndex;

  const MyFeedVideoPosterCarouselWidget({
    super.key,
    required this.carousel,
    required this.source,
    required this.pageIndex,
  });

  @override
  Widget build(BuildContext context) {
    final aspectRatio = carousel.frameWidth / carousel.frameHeight;

    return AspectRatio(
      aspectRatio: aspectRatio,
      child: FittedBox(
        fit: BoxFit.contain,
        child: SizedBox(
          width: carousel.frameWidth,
          // 40 -> action buttons height
          // 40 -> padding top and bottom
          height: carousel.frameHeight + 40 + 40,
          child: VideoPosterCarouselWidget(
            carousel: carousel,
            source: source,
            pageIndex: pageIndex,
            ignoreButtonsMargin: true,
          ),
        ),
      ),
    );
  }
}
