import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:praja/features/video_posters/models/video_poster_carousel.dart';
import 'package:praja/features/video_posters/video_poster_carousel_widget.dart';

class MyFeedVideoPosterCarouselWidget extends StatelessWidget {
  final VideoPosterCarousel carousel;
  final String source;
  final int pageIndex;

  const MyFeedVideoPosterCarouselWidget({
    super.key,
    required this.carousel,
    required this.source,
    required this.pageIndex,
  });

  @override
  Widget build(BuildContext context) {
    // Get device width and calculate responsive dimensions
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final containerWidth = screenWidth * 1; // 80% of screen width for margins

    // Calculate height based on aspect ratio
    final aspectRatio = carousel.frameWidth / carousel.frameHeight;
    final videoHeight = containerWidth / aspectRatio;

    // Add space for share actions and padding
    const shareActionsHeight = 80.0; // Space for share buttons
    final calculatedHeight = videoHeight + shareActionsHeight;

    // Limit height to reasonable bounds
    final maxAllowedHeight = screenHeight * 0.7; // 70% of screen height
    final totalHeight = calculatedHeight > maxAllowedHeight
        ? maxAllowedHeight
        : calculatedHeight;

    return Container(
      width: screenWidth * 0.7,
      // margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: AspectRatio(
        aspectRatio: aspectRatio,
        child: FittedBox(
          fit: BoxFit.contain,
          child: SizedBox(
            width:  carousel.frameWidth,
            height: carousel.frameHeight,
            child: VideoPosterCarouselWidget(
              carousel: carousel,
              source: source,
              pageIndex: pageIndex,
            ),
          ),
        ),
      ),
    );
  }
}
