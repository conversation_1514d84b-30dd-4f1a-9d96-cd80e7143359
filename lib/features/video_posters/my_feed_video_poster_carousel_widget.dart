import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:praja/features/video_posters/models/video_poster_carousel.dart';
import 'package:praja/features/video_posters/video_poster_carousel_widget.dart';

class MyFeedVideoPosterCarouselWidget extends StatelessWidget {
  final VideoPosterCarousel carousel;
  final String source;
  final int pageIndex;

  const MyFeedVideoPosterCarouselWidget({
    super.key,
    required this.carousel,
    required this.source,
    required this.pageIndex,
  });

  @override
  Widget build(BuildContext context) {
    final aspectRatio = carousel.frameWidth / carousel.frameHeight;

    return AspectRatio(
      aspectRatio: aspectRatio,
      child: FittedBox(
        fit: BoxFit.contain,
        child: SizedBox(
          width: carousel.frameWidth,
          // 40 -> action buttons height
          // 40 -> padding top and bottom
          height: carousel.frameHeight + 40 + 40,
          child: VideoPosterCarouselWidget(
            carousel: carousel,
            source: source,
            pageIndex: pageIndex,
            ignoreButtonsMargin: true,
          ),
        ),
      ),
    );
  }
}
