import 'package:flutter/material.dart';
import 'package:praja/features/video_posters/models/video_poster_carousel.dart';
import 'package:praja/features/video_posters/video_poster_carousel_widget.dart';

class MyFeedVideoPosterCarouselWidget extends StatelessWidget {
  final VideoPosterCarousel carousel;
  final String source;
  final int pageIndex;

  const MyFeedVideoPosterCarouselWidget({
    super.key,
    required this.carousel,
    required this.source,
    required this.pageIndex,
  });

  @override
  Widget build(BuildContext context) {
    // Get device width and calculate responsive dimensions
    final screenWidth = MediaQuery.of(context).size.width;
    final containerWidth = screenWidth * 0.8; // 80% of screen width for margins

    // Calculate height based on aspect ratio
    final aspectRatio = carousel.frameWidth / carousel.frameHeight;
    final videoHeight = containerWidth / aspectRatio;

    // Add padding for share actions and spacing
    final totalHeight = videoHeight + 120; // 120 for share actions and padding

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8.0,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 16.0,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.0),
        child: Container(
          width: containerWidth,
          height: totalHeight,
          color: Colors.black, // Background color for video poster
          child: VideoPosterCarouselWidget(
            carousel: carousel,
            source: source,
            pageIndex: pageIndex,
          ),
        ),
      ),
    );
  }
}
